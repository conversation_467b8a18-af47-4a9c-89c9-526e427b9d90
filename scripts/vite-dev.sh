#!/bin/bash

# Ultra-fast Vite development
export NODE_OPTIONS="--max-old-space-size=4096"
export VITE_FAST_REFRESH=true

echo "🏎️ Starting ULTRA-FAST Vite development..."

# Start backend
cd apps/backend
source venv/bin/activate
python manage.py runserver 8000 --noreload &
BACKEND_PID=$!

# Start Vite frontend
cd ../frontend
pnpm dev &
FRONTEND_PID=$!

echo "✅ Backend PID: $BACKEND_PID"
echo "✅ Vite Frontend PID: $FRONTEND_PID"
echo "🌐 Frontend: http://localhost:3001"
echo "🌐 Backend: http://localhost:8000"

# Wait for Ctrl+C
trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
